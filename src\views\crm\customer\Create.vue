<template>
  <xr-create
    :loading="loading"
    :title="title"
    @close="close"
    @save="saveClick">
    <i slot="title-append" class="wk wk-icon-fill-help wk-help-tips" data-type="8" data-id="35" />
    <create-sections title="基本信息">
      <el-form
        ref="crmForm"
        :model="fieldForm"
        :rules="fieldRules"
        :validate-on-rule-change="false"
        class="wk-form"
        label-position="top">
        <wk-form-items
          v-for="(children, index) in fieldList"
          :key="index"
          :field-from="fieldForm"
          :field-list="children"
          :ignore-fields="['customerName']"
          @change="formChange"
        >
          <template slot-scope="{ data }">
            <crm-relative-cell
              v-if="data && data.formType == 'customer'"
              :value="fieldForm[data.field]"
              :disabled="data.disabled"
              relative-type="customer"
              @value-change="otherChange($event, data)"
            />
            <xh-customer-address
              v-else-if="data && data.formType == 'map_address'"
              :value="fieldForm[data.field]"
              @value-change="otherChange($event, data)"
            />
            <div v-else-if="data.field == 'customerName'">
              <business-info-input
                v-if="canUseEnterprise"
                v-model="fieldForm[data.field]"
                :maxlength="100"
                :disabled="data.disabled"
                :debounce="1000"
                :type="data.formType"
                value-key="name"
                @select="customerNameSelect"
                @input="formChange(data, index, $event)" />
              <el-input
                v-else
                v-model.trim="fieldForm[data.field]"
                :disabled="data.disabled"
                maxlength="100"
                :placeholder="data.placeholder"
                type="text"
                @input="formChange(data, index, $event)" />
              <el-button
                type="text"
                :disabled="!businessInfo || businessInfo.name !== fieldForm[data.field]"
                class="wk-premium-info-btn"
                data-type="BusinessInformation"
                @click="checkBIDetail">
                <i class="wk wk-icon-lightning-solid wk-premium-info-icon" data-type="BusinessInformation" />
                <span class="wk-premium-info-label" data-type="BusinessInformation">工商信息</span>
              </el-button>
            </div>
          </template>
        </wk-form-items>
      </el-form>
    </create-sections>

    <el-button
      v-if="action.type == 'save' && contactsSaveAuth"
      slot="footer"
      class="handle-button"
      @click="debouncedSaveField(true)">保存并新建联系人</el-button>

    <!-- 新建 -->
    <contacts-create
      v-if="contactsCreateShow"
      :action="contactsCreateAction"
      @close="close"
      @save-success="close"
    />

    <business-info-view
      v-if="businessInfoViewShow"
      :name="businessInfo.name"
      :form="fieldForm"
      :fields="baseFields"
      @close="businessInfoViewShow = false"
    />
  </xr-create>
</template>

<script>
import { filedGetFieldAPI } from '@/api/crm/common'
import { crmCustomerSaveAPI } from '@/api/crm/customer'

import XrCreate from '@/components/XrCreate'
import CreateSections from '@/components/CreateSections'
import WkFormItems from '@/components/NewCom/WkForm/WkFormItems'
import { CrmRelativeCell } from '@/components/CreateCom'

import crmTypeModel from '@/views/crm/model/crmTypeModel'
import CustomFieldsMixin from '@/mixins/CustomFields'
import ContactsCreate from '../contacts/Create'
import BusinessInfoInput from '@/components/Premium/BusinessInfo/Input'
import BusinessInfoView from '@/components/Premium/BusinessInfo/View'

import { debounce } from 'throttle-debounce'
import { isEmpty } from '@/utils/types'
import { mapGetters } from 'vuex'

export default {
  // 新建编辑
  name: 'CustomerCreate',

  components: {
    XrCreate,
    CreateSections,
    WkFormItems,
    XhCustomerAddress: () => import('@/components/CreateCom/XhCustomerAddress'),
    ContactsCreate,
    CrmRelativeCell,
    BusinessInfoInput,
    BusinessInfoView
  },

  mixins: [CustomFieldsMixin],

  props: {
    phone: String,
    action: {
      type: Object,
      default: () => {
        return {
          type: 'save',
          id: '',
          data: {}
        }
      }
    }
  },

  data() {
    return {
      loading: false,
      baseFields: [],
      fieldList: [],
      fieldForm: {},
      fieldRules: {},
      contactsCreateAction: {
        type: 'save',
        id: '',
        data: {}
      },
      contactsCreateShow: false,

      // 工商信息
      businessInfo: null,
      businessInfoViewShow: false
    }
  },

  computed: {
    ...mapGetters(['crm', 'moduleData']),
    // 如果有次数就可以使用
    canUseEnterprise() {
      const enterprise = this.moduleData.find(item => item.module === 'enterprise')
      return enterprise ? enterprise.number > 0 : false
    },
    contactsSaveAuth() {
      return this.crm.contacts && this.crm.contacts.save
    },
    title() {
      return this.action.type === 'update' ? '编辑客户' : '新建客户'
    }
  },

  watch: {},

  created() {
    this.debouncedSaveField = debounce(300, this.saveClick)

    this.getField()
  },

  mounted() {},

  beforeDestroy() {},

  methods: {
    /**
     * 获取数据
     */
    getField() {
      this.loading = true
      const params = {
        label: crmTypeModel.customer
      }

      if (this.action.type == 'update') {
        params.id = this.action.id
      }

      filedGetFieldAPI(params)
        .then(res => {
          const list = res.data || []
          if (!isEmpty(this.phone)) {
            list.forEach(item => {
              if (item.formType === 'mobile') {
                item.defaultValue = this.phone
              }
            })
          }
          const assistIds = this.getFormAssistIds(list)

          const baseFields = []
          const fieldList = []
          const fieldRules = {}
          const fieldForm = {}
          list.forEach(children => {
            const fields = []
            children.forEach(item => {
              const temp = this.getFormItemDefaultProperty(item)
              temp.show = !assistIds.includes(item.formAssistId)

              const canEdit = this.getItemIsCanEdit(item, this.action.type)
              // 是否能编辑权限
              if (temp.show && canEdit) {
                fieldRules[temp.field] = this.getRules(item)
              }

              // 是否可编辑
              temp.disabled = !canEdit

              // 禁止某些业务组件选择
              if (temp.formType == 'customer') {
                if (this.action.type == 'relative') {
                  const relativeDisInfos = {
                    customer: { customer: true },
                    contacts: { customer: true }
                  }

                  // 在哪个类型下添加
                  const relativeTypeDisInfos = relativeDisInfos[this.action.crmType]
                  if (relativeTypeDisInfos) {
                  // 包含的字段值
                    temp.disabled = relativeTypeDisInfos[item.formType] || false
                  }
                }
              }

              if(temp.fieldName == 'region'){
                temp.setting = WKConfig.countries.map(item => item.region);
              }

              if(temp.fieldName == 'country'){
                // 初始化时国家选项为空，等待区域选择后再填充
                temp.setting = [];
              }

              // 特殊字段允许多选
              this.getItemRadio(item, temp)

              // 获取默认值
              if (temp.show) {
                fieldForm[temp.field] = this.getItemValue(item, this.action.data, this.action.type)
              }
              fields.push(temp)
              baseFields.push(item)
            })
            fieldList.push(fields)
          })

          this.baseFields = baseFields
          this.fieldList = fieldList
          this.fieldForm = fieldForm
          this.fieldRules = fieldRules

          // 初始化完成后，如果有区域值，需要设置对应的国家选项
          this.$nextTick(() => {
            this.initializeCountryOptions()
          })

          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 保存
     */
    saveClick(createContacts = false) {
      this.loading = true
      const crmForm = this.$refs.crmForm
      crmForm.validate(valid => {
        if (valid) {
          const params = this.getSubmiteParams([].concat.apply([], this.fieldList), this.fieldForm)
          this.submiteParams(params, createContacts)
        } else {
          this.loading = false
          // 提示第一个error
          this.getFormErrorMessage(crmForm)
          return false
        }
      })
    },

    /**
     * 提交上传
     */
    submiteParams(params, createContacts) {
      if (this.action.type == 'update') {
        params.entity.customerId = this.action.id
        params.entity.batchId = this.action.batchId
      }

      // 相关添加时候的多余提交信息
      if (
        this.action.relativeData &&
        Object.keys(this.action.relativeData).length
      ) {
        params = { ...params, ...this.action.relativeData }
      }
      crmCustomerSaveAPI(params)
        .then(res => {
          this.loading = false
          this.$store.dispatch('GetMessageNum')

          if (createContacts) {
            this.contactsCreateAction = {
              type: 'relative',
              crmType: 'customer',
              data: {
                customer: res.data || {}
              }
            }
            this.contactsCreateShow = true
          } else {
            this.$message.success(
              this.action.type == 'update' ? '编辑成功' : '添加成功'
            )
            this.close()
          }

          // 保存成功
          this.$emit('save-success', {
            type: 'customer',
            data: res.data || {}
          })
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 验证唯一
     */
    UniquePromise({ field, value }) {
      return this.getUniquePromise(field, value, this.action)
    },

    /**
     * change
     */
    formChange(field, index, value, valueList) {
      if ([
        'select',
        'checkbox'
      ].includes(field.formType) &&
          field.remark === 'options_type' &&
          field.optionsData) {
        const { fieldForm, fieldRules } = this.getFormContentByOptionsChange(this.fieldList, this.fieldForm)
        this.fieldForm = fieldForm
        this.fieldRules = fieldRules
      }

      // 处理区域和国家的联动逻辑
      if (field.fieldName === 'region') {
        this.handleRegionChange(value)
      }
    },

    /**
     * 处理区域变化，更新国家选项
     */
    handleRegionChange(selectedRegion) {
      // 找到国家字段
      const countryField = this.findFieldByName('country')
      if (countryField && selectedRegion) {
        // 根据选中的区域找到对应的国家列表
        const regionData = this.WKConfig.countries.find(item => item.region === selectedRegion)
        if (regionData) {
          countryField.setting = regionData.countries
        } else {
          countryField.setting = []
        }

        // 清空国家字段的值，因为区域变了
        const countryFieldName = countryField.field
        if (countryFieldName) {
          this.$set(this.fieldForm, countryFieldName, '')
        }
      } else if (countryField) {
        // 如果没有选择区域，清空国家选项
        countryField.setting = []
        const countryFieldName = countryField.field
        if (countryFieldName) {
          this.$set(this.fieldForm, countryFieldName, '')
        }
      }
    },

    /**
     * 初始化国家选项（用于编辑模式）
     */
    initializeCountryOptions() {
      const regionField = this.findFieldByName('region')
      if (regionField) {
        const regionValue = this.fieldForm[regionField.field]
        if (regionValue) {
          this.handleRegionChange(regionValue)
        }
      }
    },

    /**
     * 根据字段名查找字段配置
     */
    findFieldByName(fieldName) {
      for (const fieldGroup of this.fieldList) {
        for (const field of fieldGroup) {
          if (field.fieldName === fieldName) {
            return field
          }
        }
      }
      return null
    },

    /**
     * 地址change
     */
    otherChange(data, field) {
      this.$set(this.fieldForm, field.field, data.value)
      this.$refs.crmForm.validateField(field.field)
    },

    /**
     * 关闭
     */
    close() {
      this.contactsCreateShow = false
      this.$emit('close')
    },

    /**
     * @description: 客户名称选择
     * @param {*} item 选择的对象
     * @return {*}
     */
    customerNameSelect(item) {
      this.businessInfo = item
    },

    /**
     * @description: 查看工商详情
     * @param {*}
     * @return {*}
     */
    checkBIDetail() {
      if (!this.canUseEnterprise) return
      this.businessInfoViewShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.wk-form {
  ::v-deep .el-form-item.is-map_address {
    flex: 0 0 100%;
  }
}

.wk-premium-info-btn {
  position: absolute;
  top: -30px;
  right: 0;
}

.el-autocomplete {
  width: 100%;
}
</style>
